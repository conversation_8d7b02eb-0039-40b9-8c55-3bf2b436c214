# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml --extra executable -o requirements.txt
altgraph==0.17.4
    # via
    #   macholib
    #   pyinstaller
et-xmlfile==2.0.0
    # via openpyxl
fire==0.5.0
    # via neoxhelper (pyproject.toml)
jinja2==3.1.6
    # via pyecharts
jmespath==1.0.1
    # via neoxhelper (pyproject.toml)
loguru==0.7.3
    # via neoxhelper (pyproject.toml)
macholib==1.16.3
    # via pyinstaller
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.2
    # via jinja2
mdurl==0.1.2
    # via markdown-it-py
openpyxl==3.1.5
    # via neoxhelper (pyproject.toml)
packaging==25.0
    # via
    #   pyinstaller
    #   pyinstaller-hooks-contrib
petl==1.7.17
    # via neoxhelper (pyproject.toml)
prettytable==3.16.0
    # via pyecharts
pyecharts==2.0.8
    # via neoxhelper (pyproject.toml)
pygments==2.19.2
    # via rich
pyinstaller==6.14.2
    # via neoxhelper (pyproject.toml)
pyinstaller-hooks-contrib==2025.7
    # via pyinstaller
rich==13.7.1
    # via neoxhelper (pyproject.toml)
setuptools==80.9.0
    # via
    #   pyinstaller
    #   pyinstaller-hooks-contrib
simplejson==3.20.1
    # via pyecharts
six==1.17.0
    # via fire
termcolor==3.1.0
    # via fire
toml==0.10.2
    # via neoxhelper (pyproject.toml)
wcwidth==0.2.13
    # via prettytable
