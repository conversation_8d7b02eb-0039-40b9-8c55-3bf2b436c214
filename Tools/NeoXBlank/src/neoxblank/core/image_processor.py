"""
Core image processing functionality for blank page detection.

This module contains the main image processing logic for detecting
whether an image (particularly prescriptions) is blank or not.
"""

import urllib.request

import cv2
import numpy as np


def is_blank_page(image_path):
    """
    Check if an image is a blank page.

    This function processes an image to determine if it's essentially blank
    by analyzing the variance in pixel values after applying various
    image processing techniques.

    Args:
        image_path (str): Path to the image file or URL

    Returns:
        float: Variance value indicating how "blank" the image is.
               Lower values indicate more blank pages.
    """
    # If image_path is URL, download the image
    if image_path.startswith("http"):
        resp = urllib.request.urlopen(image_path)
        image = np.asarray(bytearray(resp.read()), dtype="uint8")
        image = cv2.imdecode(image, cv2.IMREAD_GRAYSCALE)
    else:
        image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)

    # Apply image processing pipeline
    image[image < 3] = 255
    _, image = cv2.threshold(image, 170, 255, cv2.THRESH_BINARY)
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (25, 25))
    image = cv2.morphologyEx(image, cv2.MORPH_OPEN, kernel)

    # Calculate variance to determine blankness
    mean_val = cv2.mean(image)[0]
    variance = cv2.mean(cv2.subtract(image, mean_val))[0]

    return variance
