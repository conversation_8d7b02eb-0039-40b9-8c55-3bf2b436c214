"""
HTTP API server for NeoXBlank image blank detection service.

This module provides a Sanic-based HTTP API for the image blank detection
functionality, allowing remote clients to check if images are blank.
"""

from sanic import Sanic, response

from ..core.image_processor import is_blank_page


def create_app():
    """
    Create and configure the Sanic application.

    Returns:
        Sanic: Configured Sanic application instance
    """
    app = Sanic("ImageBlankChecker")

    @app.route("/check_blank", methods=["POST"])
    async def check_blank(request):
        """
        API endpoint to check if an image is blank.

        Expected JSON payload:
        {
            "image_path": "path/to/image.jpg" or "http://example.com/image.jpg"
        }

        Returns:
        {
            "result": float  # Variance value indicating blankness
        }
        """
        try:
            image_path = request.json.get("image_path")
            if not image_path:
                return response.json({"error": "image_path is required"}, status=400)

            result = is_blank_page(image_path)
            return response.json({"result": result})

        except Exception as e:
            return response.json(
                {"error": f"Failed to process image: {str(e)}"}, status=500
            )

    @app.route("/health", methods=["GET"])
    async def health_check(_request):
        """Health check endpoint."""
        return response.json({"status": "healthy"})

    return app


def run_server(host="0.0.0.0", port=6999, debug=False):
    """
    Run the HTTP server.

    Args:
        host (str): Host to bind to
        port (int): Port to bind to
        debug (bool): Enable debug mode
    """
    app = create_app()
    app.run(host=host, port=port, debug=debug)
