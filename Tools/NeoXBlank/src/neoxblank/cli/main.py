"""
Command-line interface for NeoXBlank.

This module provides both CLI and server functionality for the
NeoXBlank image blank detection tool.
"""

import argparse
import sys

from ..api.server import run_server
from ..core.image_processor import is_blank_page


def main():
    """
    Main entry point for the NeoXBlank CLI.

    Supports both direct image processing and HTTP server mode.
    """
    parser = argparse.ArgumentParser(
        description="NeoXBlank - Image blank detection tool"
    )

    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    # Check command for direct image processing
    check_parser = subparsers.add_parser("check", help="Check if an image is blank")
    check_parser.add_argument("image_path", help="Path to image file or URL")

    # Server command for HTTP API
    server_parser = subparsers.add_parser("server", help="Start HTTP API server")
    server_parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    server_parser.add_argument("--port", type=int, default=6999, help="Port to bind to")
    server_parser.add_argument("--debug", action="store_true", help="Enable debug mode")

    args = parser.parse_args()

    if args.command == "check":
        try:
            result = is_blank_page(args.image_path)
            print(f"Blank detection result: {result}")
            return 0
        except Exception as e:
            print(f"Error processing image: {e}", file=sys.stderr)
            return 1

    elif args.command == "server":
        print(f"Starting NeoXBlank server on {args.host}:{args.port}")
        try:
            run_server(host=args.host, port=args.port, debug=args.debug)
            return 0
        except Exception as e:
            print(f"Error starting server: {e}", file=sys.stderr)
            return 1
    else:
        # Default behavior: start server (for backward compatibility)
        print("Starting NeoXBlank server on 0.0.0.0:6999")
        print("Use 'neoxblank --help' for more options")
        try:
            run_server()
            return 0
        except Exception as e:
            print(f"Error starting server: {e}", file=sys.stderr)
            return 1


if __name__ == "__main__":
    sys.exit(main())
