#!/usr/bin/env python3
"""
Build script for creating Linux binary executable using PyInstaller.

This script automates the process of building a standalone binary
executable for the NeoXBlank tool.
"""

import os
import shutil
import subprocess
import sys
from pathlib import Path


def run_command(cmd, cwd=None):
    """Run a shell command and return the result."""
    print(f"Running: {cmd}")
    result = subprocess.run(cmd, shell=True, cwd=cwd, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"Error: {result.stderr}")
        sys.exit(1)
    return result.stdout


def main():
    """Main build function."""
    # Get project root directory
    script_dir = Path(__file__).parent
    project_root = script_dir.parent

    print("🚀 Building NeoXBlank binary executable...")
    print(f"Project root: {project_root}")

    # Change to project directory
    os.chdir(project_root)

    # Clean previous builds
    print("🧹 Cleaning previous builds...")
    for dir_name in ["build", "dist"]:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"Removed {dir_name}/")

    # Install build dependencies if not already installed
    print("📦 Installing build dependencies...")
    run_command("uv sync --group build")

    # Create PyInstaller spec file
    print("📝 Creating PyInstaller spec file...")
    spec_content = """
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['src/neoxblank/cli/main.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'cv2',
        'numpy',
        'sanic',
        'neoxblank.core.image_processor',
        'neoxblank.api.server',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='neoxblank',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
"""

    with open("neoxblank.spec", "w") as f:
        f.write(spec_content.strip())

    # Build the binary
    print("🔨 Building binary with PyInstaller...")
    run_command("uv run pyinstaller neoxblank.spec --clean")

    # Check if binary was created successfully
    binary_path = "dist/neoxblank"
    if os.path.exists(binary_path):
        print(f"✅ Binary successfully created: {binary_path}")

        # Make it executable
        run_command(f"chmod +x {binary_path}")

        # Test the binary
        print("🧪 Testing the binary...")
        result = subprocess.run([binary_path, "--help"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Binary test passed!")
            print("\n📋 Build Summary:")
            print(f"   Binary location: {os.path.abspath(binary_path)}")
            print(
                f"   Binary size: {os.path.getsize(binary_path) / 1024 / 1024:.1f} MB"
            )
            print("\n🚀 You can now deploy the binary to your Linux server!")
        else:
            print("❌ Binary test failed!")
            print(f"Error: {result.stderr}")
            sys.exit(1)
    else:
        print("❌ Binary creation failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
