# [build-system]
# requires = ["hatchling"]
# build-backend = "hatchling.build"

[project]
name = "neoxmetrics"
version = "1.0.0"
description = "A modern CLI tool for Prometheus metrics monitoring and Redis TimeSeries data processing"
readme = "README.md"
license = "MIT"
authors = [{ name = "Kuno Lu", email = "<EMAIL>" }]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Operating System :: OS Independent",
    "Topic :: System :: Monitoring",
    "Topic :: Database :: Database Engines/Servers",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
keywords = ["prometheus", "redis", "timeseries", "monitoring", "metrics", "cli"]
requires-python = ">=3.10"

dependencies = [
    "pendulum==3.1.0",
    "prometheus-client==0.20.0",
    "redis==5.0.8",
    "toml==0.10.2",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-asyncio>=0.21.0",
    "ruff>=0.1.0",
    "mypy>=1.0.0",
    "types-redis>=4.0.0",
    "types-toml>=0.10.0",
]
executable = ["pyinstaller>=6.0.0"]

[project.urls]
Repository = "https://bitbucket.org/neoxinc/testing/src/main/Tools/NeoXMetrics/"

[project.scripts]
neoxmetrics = "neox_metrics:main"

# [tool.hatch.build.targets.wheel]
# packages = ["common"]
# include = ["neox_metrics.py", "metrics.toml"]

# [tool.hatch.build.targets.sdist]
# include = [
#     "/common",
#     "/neox_metrics.py",
#     "/metrics.toml",
#     "/README.md",
#     "/pyproject.toml",
# ]

[tool.uv.pip]
index-url = "https://mirrors.aliyun.com/pypi/simple"

# Ruff configuration
[tool.ruff]
target-version = "py310"
line-length = 88
src = ["."]

[tool.ruff.lint]
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # pyflakes
    "I",   # isort
    "B",   # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
    "ARG", # flake8-unused-arguments
    "SIM", # flake8-simplify
    "TCH", # flake8-type-checking
]
ignore = [
    "E501",   # line too long, handled by formatter
    "B008",   # do not perform function calls in argument defaults
    "C901",   # too complex
    "ARG002", # unused method argument
]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401"]       # unused imports in __init__.py
"tests/**/*" = ["ARG", "S101"] # allow unused args and assert in tests

[tool.ruff.lint.isort]
known-first-party = ["neoxmetrics", "common"]
force-single-line = false
split-on-trailing-comma = true

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

# MyPy configuration
[tool.mypy]
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

# Pytest configuration
[tool.pytest.ini_options]
testpaths = ["tests", "."]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short --cov=common --cov=neox_metrics"
minversion = "7.0"
